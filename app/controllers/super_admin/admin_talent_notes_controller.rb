class SuperAdmin::AdminTalentNotesController < SuperAdmin::AdminBaseController
  include SuperAdmin::CsvExportable
  include AdminBulkOperations

  before_action :set_talent_note, only: [:show, :edit, :update, :destroy]

  def index
    @talent_notes = TalentNote.includes(:user, :talent_profile => :user, :organization, :last_modified_by)
                              .order(created_at: :desc)

    # Apply search
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      @talent_notes = @talent_notes.joins(:user, :talent_profile => :user, :organization)
                                  .where(
                                    "talent_notes.content ILIKE ? OR users.first_name ILIKE ? OR users.last_name ILIKE ? OR users.email ILIKE ? OR talent_users.first_name ILIKE ? OR talent_users.last_name ILIKE ? OR talent_users.email ILIKE ? OR organizations.name ILIKE ?",
                                    search_term, search_term, search_term, search_term, search_term, search_term, search_term, search_term
                                  )
                                  .references(:talent_profiles)
    end

    # Apply filters
    if params[:user_id].present? && params[:user_id] != 'all'
      @talent_notes = @talent_notes.where(user_id: params[:user_id])
    end

    if params[:talent_profile_id].present? && params[:talent_profile_id] != 'all'
      @talent_notes = @talent_notes.where(talent_profile_id: params[:talent_profile_id])
    end

    if params[:organization_id].present? && params[:organization_id] != 'all'
      @talent_notes = @talent_notes.where(organization_id: params[:organization_id])
    end

    if params[:category].present? && params[:category] != 'all'
      @talent_notes = @talent_notes.where(category: params[:category])
    end

    if params[:pinned].present? && params[:pinned] != 'all'
      @talent_notes = @talent_notes.where(pinned: params[:pinned] == 'true')
    end

    if params[:date_range].present? && params[:date_range] != 'all'
      case params[:date_range]
      when 'today'
        @talent_notes = @talent_notes.where(created_at: Date.current.beginning_of_day..Date.current.end_of_day)
      when 'week'
        @talent_notes = @talent_notes.where(created_at: 1.week.ago..Time.current)
      when 'month'
        @talent_notes = @talent_notes.where(created_at: 1.month.ago..Time.current)
      end
    end

    # Get filter options
    @available_users = User.joins(:talent_notes).distinct.select(:id, :first_name, :last_name, :email).order(:first_name, :last_name)
    @available_talents = TalentProfile.joins(:talent_notes).includes(:user).distinct.order('users.first_name, users.last_name')
    @available_organizations = Organization.joins(:talent_notes).distinct.order(:name)
    @note_categories = TalentNote.categories.keys

    # Paginate results
    @pagy, @talent_notes = pagy(@talent_notes, limit: @page_size)

    # Stats for dashboard
    @stats = {
      total: TalentNote.count,
      pinned: TalentNote.pinned.count,
      unique_authors: TalentNote.distinct.count(:user_id),
      unique_talents: TalentNote.distinct.count(:talent_profile_id),
      notes_this_week: TalentNote.where(created_at: 1.week.ago..Time.current).count,
      avg_notes_per_talent: calculate_avg_notes_per_talent
    }
  end

  def show
  end

  def edit
  end

  def update
    @talent_note.last_modified_by = Current.user
    
    if @talent_note.update(talent_note_params)
      redirect_to super_admin_admin_talent_note_path(@talent_note),
                  notice: 'Talent note updated successfully.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    author_name = @talent_note.user.name.full
    talent_name = @talent_note.talent_profile.user.name.full
    
    @talent_note.destroy
    redirect_to super_admin_admin_talent_notes_path,
                notice: "Deleted note by #{author_name} about #{talent_name}."
  end

  # Bulk operations
  def bulk_update
    talent_note_ids = params[:talent_note_ids]
    action = params[:bulk_action]

    return redirect_to super_admin_admin_talent_notes_path, alert: 'No talent notes selected.' if talent_note_ids.blank?

    talent_notes = TalentNote.where(id: talent_note_ids)

    case action
    when 'delete'
      count = talent_notes.count
      talent_notes.destroy_all
      redirect_to super_admin_admin_talent_notes_path, notice: "#{count} talent notes deleted successfully."
    when 'pin'
      count = talent_notes.update_all(pinned: true, last_modified_by_id: Current.user.id)
      redirect_to super_admin_admin_talent_notes_path, notice: "#{count} talent notes pinned successfully."
    when 'unpin'
      count = talent_notes.update_all(pinned: false, last_modified_by_id: Current.user.id)
      redirect_to super_admin_admin_talent_notes_path, notice: "#{count} talent notes unpinned successfully."
    else
      redirect_to super_admin_admin_talent_notes_path, alert: 'Invalid bulk action.'
    end
  end

  protected

  def resource_name
    'talent_notes'
  end

  private

  def set_talent_note
    @talent_note = TalentNote.find(params[:id])
  end

  def talent_note_params
    params.require(:talent_note).permit(:content, :category, :pinned)
  end

  def collection_for_export
    @talent_notes_for_export || @talent_notes
  end

  def calculate_avg_notes_per_talent
    total_notes = TalentNote.count
    unique_talents = TalentNote.distinct.count(:talent_profile_id)
    return 0 if unique_talents.zero?
    (total_notes.to_f / unique_talents).round(1)
  end

  # CSV Export methods
  def generate_talent_notes_csv
    CSV.generate(headers: true) do |csv|
      csv << [
        'ID',
        'Author Name',
        'Author Email',
        'Talent Name',
        'Talent Email',
        'Organization',
        'Category',
        'Content Preview',
        'Pinned',
        'Last Modified By',
        'Created At',
        'Updated At'
      ]

      collection_for_export.includes(:user, :talent_profile => :user, :organization, :last_modified_by).find_each do |note|
        content_preview = if note.content.respond_to?(:to_plain_text)
                           note.content.to_plain_text.truncate(100)
                         else
                           note.content.to_s.truncate(100)
                         end

        csv << [
          note.id,
          note.user.name.full,
          note.user.email,
          note.talent_profile.user.name.full,
          note.talent_profile.user.email,
          note.organization.name,
          note.category.humanize,
          content_preview,
          note.pinned? ? 'Yes' : 'No',
          note.last_modified_by&.name&.full || 'N/A',
          note.created_at.strftime('%Y-%m-%d %H:%M'),
          note.updated_at.strftime('%Y-%m-%d %H:%M')
        ]
      end
    end
  end
end
