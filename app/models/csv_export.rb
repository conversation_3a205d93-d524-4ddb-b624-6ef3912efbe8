class CsvExport < ApplicationRecord
  belongs_to :admin_user, class_name: 'User'
  
  enum status: {
    queued: 0,
    processing: 1,
    completed: 2,
    failed: 3
  }
  
  validates :export_id, presence: true, uniqueness: true
  validates :controller_name, presence: true
  validates :status, presence: true
  
  scope :recent, -> { order(created_at: :desc) }
  scope :for_user, ->(user) { where(admin_user: user) }
  
  def filename
    "#{controller_name}_#{created_at.strftime('%Y%m%d')}_#{export_id[0..7]}.csv"
  end
  
  def display_name
    controller_name.humanize.gsub('Admin ', '')
  end
  
  def progress_percentage
    case status
    when 'queued'
      0
    when 'processing'
      50
    when 'completed', 'failed'
      100
    else
      0
    end
  end
  
  def status_color
    case status
    when 'queued'
      'yellow'
    when 'processing'
      'blue'
    when 'completed'
      'green'
    when 'failed'
      'red'
    else
      'gray'
    end
  end
  
  def status_icon
    case status
    when 'queued'
      '⏳'
    when 'processing'
      '⚙️'
    when 'completed'
      '✅'
    when 'failed'
      '❌'
    else
      '❓'
    end
  end
end
