# == Schema Information
#
# Table name: organizations
#
#  id                 :bigint           not null, primary key
#  jobs_count         :integer          default(0), not null
#  name               :string           not null
#  operating_timezone :string
#  size               :string
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#
# Indexes
#
#  index_organizations_on_created_at  (created_at)
#  index_organizations_on_name        (name)
#
class Organization < ApplicationRecord
  has_many :jobs, dependent: :destroy
  has_many :organization_memberships, dependent: :destroy
  has_many :users, through: :organization_memberships
  has_one_attached :logo
end
