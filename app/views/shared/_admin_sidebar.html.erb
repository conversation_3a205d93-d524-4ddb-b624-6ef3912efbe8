<nav class="flex flex-col h-full" data-controller="admin-sidebar">
  <!-- Sidebar Header -->
  <div class="flex items-center justify-between px-4 py-4 border-b border-stone-200">
    <div class="flex items-center space-x-2">
      <div class="w-8 h-8 bg-stone-900 rounded-lg flex items-center justify-center">
        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
        </svg>
      </div>
      <span class="admin-sidebar-text font-semibold text-stone-900">Admin</span>
    </div>
    <button type="button"
            class="lg:hidden p-1 rounded-md text-stone-400 hover:text-stone-600 hover:bg-stone-100 admin-focus-ring"
            data-action="click->admin-sidebar#toggle"
            aria-label="Toggle sidebar">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>
  </div>

  <div class="flex-1 px-4 py-6 space-y-2">
    <!-- Dashboard -->
    <%= link_to super_admin_admin_dashboard_path,
        class: "#{admin_sidebar_link_classes(request.path.include?('admin_dashboard'))}" do %>
      <svg class="admin-sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
      </svg>
      <span class="admin-sidebar-text">Dashboard</span>
    <% end %>

    <!-- Users -->
    <%= link_to super_admin_admin_users_path,
        class: "#{admin_sidebar_link_classes(request.path.include?('admin_users'))}" do %>
      <svg class="admin-sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
      </svg>
      <span class="admin-sidebar-text">Users</span>
    <% end %>

    <!-- Jobs -->
    <%= link_to super_admin_admin_jobs_path,
        class: "#{admin_sidebar_link_classes(request.path.include?('admin_jobs'))}" do %>
      <svg class="admin-sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6z"></path>
      </svg>
      <span class="admin-sidebar-text">Jobs</span>
    <% end %>

    <!-- Organizations -->
    <%= link_to super_admin_admin_organizations_path,
        class: "#{admin_sidebar_link_classes(request.path.include?('admin_organizations'))}" do %>
      <svg class="admin-sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2-2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
      </svg>
      <span class="admin-sidebar-text">Organizations</span>
    <% end %>

    <!-- Chat Requests -->
    <%= link_to super_admin_admin_chat_requests_path,
        class: "#{admin_sidebar_link_classes(request.path.include?('admin_chat_requests'))}" do %>
      <svg class="admin-sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
      </svg>
      <span class="admin-sidebar-text">Chat Requests</span>
    <% end %>

    <!-- Conversations -->
    <%= link_to super_admin_admin_conversations_path,
        class: "#{admin_sidebar_link_classes(request.path.include?('admin_conversations'))}" do %>
      <svg class="admin-sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2v-6a2 2 0 012-2h8z"></path>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12H9m6-4H9"></path>
      </svg>
      <span class="admin-sidebar-text">Conversations</span>
    <% end %>

    <!-- Job Applications -->
    <%= link_to super_admin_admin_job_applications_path,
        class: "#{admin_sidebar_link_classes(request.path.include?('admin_job_applications'))}" do %>
      <svg class="admin-sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <span class="admin-sidebar-text">Applications</span>
    <% end %>

    <!-- Messages -->
    <%= link_to super_admin_admin_messages_path,
        class: "#{admin_sidebar_link_classes(request.path.include?('admin_messages'))}" do %>
      <svg class="admin-sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
      </svg>
      <span class="admin-sidebar-text">Messages</span>
    <% end %>

    <!-- Talent Profiles -->
    <%= link_to super_admin_admin_talent_profiles_path,
        class: "#{admin_sidebar_link_classes(request.path.include?('admin_talent_profiles'))}" do %>
      <svg class="admin-sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
      </svg>
      <span class="admin-sidebar-text">Talent Profiles</span>
    <% end %>

    <!-- Divider -->
    <div class="border-t border-stone-200 my-4"></div>

    <!-- Admin Management (Superadmin only) -->
    <% if Current.user.superadmin? %>
      <div class="px-3 py-2">
        <p class="text-xs font-semibold text-stone-400 uppercase tracking-wider">Administration</p>
      </div>

      <%= link_to super_admin_admin_roles_path,
          class: "#{admin_sidebar_link_classes(request.path.include?('admin_roles'))}" do %>
        <svg class="admin-sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
        </svg>
        <span class="admin-sidebar-text">Admin Roles</span>
      <% end %>

      <%= link_to super_admin_admin_audit_logs_path,
          class: "#{admin_sidebar_link_classes(request.path.include?('admin_audit_logs'))}" do %>
        <svg class="admin-sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <span class="admin-sidebar-text">Audit Logs</span>
      <% end %>

      <%= link_to super_admin_csv_exports_path,
          class: "#{admin_sidebar_link_classes(request.path.include?('csv_exports'))}" do %>
        <svg class="admin-sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <span class="admin-sidebar-text">CSV Exports</span>
      <% end %>

      <%= link_to super_admin_saved_searches_path,
          class: "#{admin_sidebar_link_classes(request.path.include?('saved_searches'))}" do %>
        <svg class="admin-sidebar-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
        <span class="admin-sidebar-text">Saved Searches</span>
      <% end %>
    <% end %>
  </div>
</nav>



<%
  def admin_sidebar_link_classes(active = false)
    base_classes = "admin-sidebar-link admin-focus-ring"
    if active
      "#{base_classes} admin-sidebar-link-active"
    else
      "#{base_classes} admin-sidebar-link-inactive"
    end
  end
%>
