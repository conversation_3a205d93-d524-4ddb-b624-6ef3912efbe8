<div class="space-y-6">
  <!-- Header -->
  <div class="flex justify-between items-center">
    <div>
      <h1 class="text-2xl font-bold text-stone-900">Security Alerts</h1>
      <p class="text-stone-600 mt-1">Monitor and manage security alerts across the platform</p>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
    <div class="bg-white rounded-lg shadow-sm border border-stone-200 p-4">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-stone-500">Total Alerts</p>
          <p class="text-lg font-semibold text-stone-900"><%= @stats[:total] %></p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-stone-200 p-4">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-stone-500">Unresolved</p>
          <p class="text-lg font-semibold text-stone-900"><%= @stats[:unresolved] %></p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-stone-200 p-4">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-stone-500">Critical</p>
          <p class="text-lg font-semibold text-stone-900"><%= @stats[:critical] %></p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-stone-200 p-4">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-stone-500">High Priority</p>
          <p class="text-lg font-semibold text-stone-900"><%= @stats[:high_priority] %></p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-stone-200 p-4">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium text-stone-500">Recent (24h)</p>
          <p class="text-lg font-semibold text-stone-900"><%= @stats[:recent] %></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-white rounded-lg shadow-sm border border-stone-200 p-6">
    <%= form_with url: super_admin_security_alerts_path, method: :get, local: true, class: "space-y-4" do |form| %>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Status Filter -->
        <div>
          <%= form.label :status, "Status", class: "block text-sm font-medium text-stone-700 mb-1" %>
          <%= form.select :status, 
                          options_for_select([['All', 'all'], ['Unresolved', 'unresolved'], ['Resolved', 'resolved']], params[:status]),
                          {}, 
                          { class: "w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
        </div>

        <!-- Severity Filter -->
        <div>
          <%= form.label :severity, "Severity", class: "block text-sm font-medium text-stone-700 mb-1" %>
          <%= form.select :severity, 
                          options_for_select([['All Severities', 'all']] + @available_severities, params[:severity]),
                          {}, 
                          { class: "w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
        </div>

        <!-- Alert Type Filter -->
        <div>
          <%= form.label :alert_type, "Alert Type", class: "block text-sm font-medium text-stone-700 mb-1" %>
          <%= form.select :alert_type, 
                          options_for_select([['All Types', 'all']] + @available_alert_types, params[:alert_type]),
                          {}, 
                          { class: "w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
        </div>

        <!-- User Filter -->
        <div>
          <%= form.label :user_filter, "User", class: "block text-sm font-medium text-stone-700 mb-1" %>
          <%= form.select :user_filter, 
                          options_for_select([['All Users', 'all']] + options_from_collection_for_select(@available_users, :id, :email, params[:user_filter])),
                          {}, 
                          { class: "w-full rounded-md border-stone-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" } %>
        </div>
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-2">
          <%= form.submit "Apply Filters", class: "bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500" %>
          <%= link_to "Clear Filters", super_admin_security_alerts_path, class: "bg-stone-200 text-stone-700 px-4 py-2 rounded-md hover:bg-stone-300 focus:outline-none focus:ring-2 focus:ring-stone-500" %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Alerts Table -->
  <div class="bg-white rounded-lg shadow-sm border border-stone-200 overflow-hidden">
    <div class="px-6 py-4 border-b border-stone-200">
      <h2 class="text-lg font-semibold text-stone-900">
        Security Alerts 
        <span class="text-sm font-normal text-stone-500">(<%= @pagy.count %> total)</span>
      </h2>
    </div>

    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-stone-200">
        <thead class="bg-stone-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Alert</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">User</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Severity</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Created</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-stone-200">
          <% if @security_alerts.any? %>
            <% @security_alerts.each do |alert| %>
              <tr class="hover:bg-stone-50">
                <td class="px-6 py-4">
                  <div>
                    <div class="text-sm font-medium text-stone-900"><%= alert.alert_type.humanize.titleize %></div>
                    <div class="text-sm text-stone-500 truncate max-w-xs"><%= alert.description %></div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div>
                      <div class="text-sm font-medium text-stone-900"><%= alert.user_name %></div>
                      <div class="text-sm text-stone-500"><%= alert.user_email %></div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= alert.severity_badge_color %>">
                    <%= alert.severity.humanize %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if alert.resolved? %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Resolved
                    </span>
                  <% else %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      Unresolved
                    </span>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                  <%= time_ago_in_words(alert.created_at) %> ago
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <%= link_to "View", super_admin_security_alert_path(alert),
                      class: "text-blue-600 hover:text-blue-900 mr-3" %>
                  <% unless alert.resolved? %>
                    <%= link_to "Resolve", resolve_super_admin_security_alert_path(alert),
                        method: :patch,
                        class: "text-green-600 hover:text-green-900",
                        data: { confirm: "Are you sure you want to resolve this alert?" } %>
                  <% end %>
                </td>
              </tr>
            <% end %>
          <% else %>
            <tr>
              <td colspan="6" class="px-6 py-12 text-sm text-center text-stone-900">
                <div class="text-stone-500">
                  <svg class="w-12 h-12 mx-auto mb-4 text-stone-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                  </svg>
                  <p class="text-sm font-medium">No security alerts found</p>
                  <p class="text-xs">Try adjusting your search or filter criteria</p>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-stone-200">
      <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
    </div>
  </div>
</div>
