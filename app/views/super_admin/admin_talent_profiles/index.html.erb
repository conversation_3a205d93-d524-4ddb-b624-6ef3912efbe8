<% content_for :title, "Talent Profiles" %>

<div class="space-y-6">
  <!-- Header -->
  <div class="border-b border-stone-200 pb-5">
    <h1 class="text-2xl font-bold leading-7 text-stone-900 sm:truncate sm:text-3xl sm:tracking-tight">
      Talent Profiles
    </h1>
    <p class="mt-2 text-sm text-stone-600">
      Manage and monitor talent profiles and their information
    </p>
  </div>

  <!-- Search and Filters -->
  <%= form_with url: super_admin_admin_talent_profiles_path, method: :get, local: true, class: "bg-white shadow rounded-lg p-6 space-y-4" do |form| %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Search -->
      <div>
        <%= form.label :search, "Search", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.text_field :search, 
            value: params[:search], 
            placeholder: "Search name, email, bio...", 
            class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
      </div>

      <!-- Availability Status Filter -->
      <div>
        <%= form.label :availability_status, "Availability", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.select :availability_status, 
            options_for_select([
              ['All', ''],
              ['Available', 'available'],
              ['Limited', 'limited'],
              ['Unavailable', 'unavailable']
            ], params[:availability_status]), 
            {}, 
            { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" } %>
      </div>

      <!-- Pricing Model Filter -->
      <div>
        <%= form.label :pricing_model, "Pricing Model", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.select :pricing_model, 
            options_for_select([
              ['All', ''],
              ['Hourly', 'hourly'],
              ['Fixed Price', 'fixed_price'],
              ['Retainer', 'retainer'],
              ['Project Based', 'project_based']
            ], params[:pricing_model]), 
            {}, 
            { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" } %>
      </div>

      <!-- Agency Status Filter -->
      <div>
        <%= form.label :agency_status, "Type", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.select :agency_status, 
            options_for_select([
              ['All', ''],
              ['Agency', 'agency'],
              ['Individual', 'individual']
            ], params[:agency_status]), 
            {}, 
            { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" } %>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <!-- Location Preference Filter -->
      <div>
        <%= form.label :location_preference, "Location Preference", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.select :location_preference, 
            options_for_select([
              ['All', ''],
              ['North America', 'north_america'],
              ['United Kingdom', 'united_kingdom'],
              ['Europe', 'europe'],
              ['Africa', 'africa'],
              ['Asia', 'asia'],
              ['South America', 'south_america'],
              ['India', 'india']
            ], params[:location_preference]), 
            {}, 
            { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" } %>
      </div>

      <!-- Premium Status Filter -->
      <div>
        <%= form.label :premium_status, "Premium Status", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.select :premium_status, 
            options_for_select([
              ['All', ''],
              ['Premium', 'premium'],
              ['Standard', 'standard']
            ], params[:premium_status]), 
            {}, 
            { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" } %>
      </div>

      <!-- Profile Completeness Filter -->
      <div>
        <%= form.label :profile_completeness, "Profile Status", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.select :profile_completeness, 
            options_for_select([
              ['All', ''],
              ['Complete', 'complete'],
              ['Incomplete', 'incomplete']
            ], params[:profile_completeness]), 
            {}, 
            { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" } %>
      </div>

      <!-- Per Page -->
      <div>
        <%= form.label :per_page, "Per Page", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.select :per_page, 
            options_for_select([
              ['25', 25],
              ['50', 50],
              ['100', 100]
            ], params[:per_page] || 25), 
            {}, 
            { class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" } %>
      </div>
    </div>

    <!-- Date Range Filters -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <%= form.label :date_from, "From Date", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.date_field :date_from, 
            value: params[:date_from], 
            class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
      </div>
      <div>
        <%= form.label :date_to, "To Date", class: "block text-sm font-medium text-stone-700 mb-1" %>
        <%= form.date_field :date_to, 
            value: params[:date_to], 
            class: "block w-full rounded-md border-stone-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" %>
      </div>
    </div>

    <div class="flex justify-end">
      <%= form.submit "Filter", class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
    </div>
  <% end %>

  <!-- Results Count and Export -->
  <div class="flex justify-between items-center">
    <p class="text-sm text-stone-600">
      Showing <%= @pagy.from %> to <%= @pagy.to %> of <%= @pagy.count %> talent profiles
    </p>
    <div class="flex space-x-2">
      <%= link_to "Export CSV", request.params.merge(format: :csv),
          class: "inline-flex items-center px-3 py-2 border border-stone-300 shadow-sm text-sm leading-4 font-medium rounded-md text-stone-700 bg-white hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
    </div>
  </div>

  <!-- Talent Profiles Table -->
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <table class="min-w-full divide-y divide-stone-200">
      <thead class="bg-stone-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            Talent
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            Headline
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            Location
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            Availability
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            Pricing
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            Status
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            Created
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-stone-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-stone-200">
        <% if @talent_profiles.any? %>
          <% @talent_profiles.each do |profile| %>
            <tr class="hover:bg-stone-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-stone-100 flex items-center justify-center">
                      <span class="text-sm font-medium text-stone-600">
                        <%= profile.user.initials %>
                      </span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-stone-900">
                      <%= profile.user.full_name %>
                    </div>
                    <div class="text-sm text-stone-500">
                      <%= profile.user.email %>
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-stone-900 max-w-xs truncate">
                  <%= profile.headline.present? ? profile.headline : "No headline" %>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                <%= profile.location.present? ? profile.location : "Not specified" %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <% case profile.availability_status %>
                <% when 'available' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-700">
                    Available
                  </span>
                <% when 'limited' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-700">
                    Limited
                  </span>
                <% when 'unavailable' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-700">
                    Unavailable
                  </span>
                <% end %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-900">
                <div>
                  <div class="font-medium"><%= profile.pricing_model.humanize %></div>
                  <% if profile.price_range_min.present? || profile.price_range_max.present? %>
                    <div class="text-stone-500">
                      <% if profile.price_range_min.present? && profile.price_range_max.present? %>
                        $<%= profile.price_range_min.to_i %> - $<%= profile.price_range_max.to_i %>
                      <% elsif profile.price_range_min.present? %>
                        From $<%= profile.price_range_min.to_i %>
                      <% elsif profile.price_range_max.present? %>
                        Up to $<%= profile.price_range_max.to_i %>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex space-x-1">
                  <% if profile.is_premium %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                      Premium
                    </span>
                  <% end %>
                  <% if profile.is_agency %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Agency
                    </span>
                  <% end %>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-stone-500">
                <div>
                  <div><%= profile.created_at.strftime("%b %d, %Y") %></div>
                  <div><%= profile.created_at.strftime("%I:%M %p") %></div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <%= link_to "View", super_admin_admin_talent_profile_path(profile),
                    class: "text-indigo-600 hover:text-indigo-900" %>
              </td>
            </tr>
          <% end %>
        <% else %>
          <tr>
            <td colspan="8" class="px-6 py-12 text-sm text-center text-stone-900">
              <div class="text-stone-500">
                <svg class="w-12 h-12 mx-auto mb-4 text-stone-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <p class="text-sm font-medium">No talent profiles found</p>
                <p class="text-xs">Try adjusting your search or filter criteria</p>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <!-- Pagination -->
  <%= render 'shared/admin/pagination', pagy: @pagy %>
</div>
