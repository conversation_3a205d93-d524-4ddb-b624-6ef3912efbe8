# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_06_29_151009) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "admin_audit_logs", force: :cascade do |t|
    t.bigint "admin_user_id", null: false
    t.string "action", null: false
    t.string "controller", null: false
    t.string "resource_type"
    t.bigint "resource_id"
    t.json "change_data"
    t.string "ip_address"
    t.string "user_agent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action"], name: "index_admin_audit_logs_on_action"
    t.index ["admin_user_id", "created_at"], name: "index_admin_audit_logs_on_admin_user_id_and_created_at"
    t.index ["admin_user_id"], name: "index_admin_audit_logs_on_admin_user_id"
    t.index ["controller"], name: "index_admin_audit_logs_on_controller"
    t.index ["created_at"], name: "index_admin_audit_logs_on_created_at"
    t.index ["resource_type", "created_at"], name: "index_admin_audit_logs_on_resource_type_and_created_at"
    t.index ["resource_type", "resource_id"], name: "index_admin_audit_logs_on_resource"
    t.index ["resource_type", "resource_id"], name: "index_admin_audit_logs_on_resource_type_and_resource_id"
  end

  create_table "chat_requests", force: :cascade do |t|
    t.bigint "scout_id", null: false
    t.bigint "talent_id", null: false
    t.integer "status", default: 0, null: false
    t.datetime "requested_at"
    t.datetime "accepted_at"
    t.datetime "declined_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "pitch"
    t.index ["created_at"], name: "index_chat_requests_on_created_at"
    t.index ["scout_id", "status"], name: "index_chat_requests_on_scout_id_and_status"
    t.index ["scout_id", "talent_id"], name: "index_chat_requests_on_scout_id_and_talent_id", unique: true
    t.index ["scout_id"], name: "index_chat_requests_on_scout_id"
    t.index ["status"], name: "index_chat_requests_on_status"
    t.index ["talent_id", "status"], name: "index_chat_requests_on_talent_id_and_status"
    t.index ["talent_id"], name: "index_chat_requests_on_talent_id"
  end

  create_table "conversation_participants", force: :cascade do |t|
    t.bigint "conversation_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "archived"
    t.boolean "bookmarked"
    t.index ["conversation_id"], name: "index_conversation_participants_on_conversation_id"
    t.index ["user_id"], name: "index_conversation_participants_on_user_id"
  end

  create_table "conversations", force: :cascade do |t|
    t.bigint "job_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "messages_count", default: 0, null: false
    t.index ["created_at"], name: "index_conversations_on_created_at"
    t.index ["job_id", "created_at"], name: "index_conversations_on_job_id_and_created_at"
    t.index ["job_id"], name: "index_conversations_on_job_id"
    t.index ["updated_at"], name: "index_conversations_on_updated_at"
  end

  create_table "csv_exports", force: :cascade do |t|
    t.string "export_id", null: false
    t.bigint "admin_user_id", null: false
    t.string "controller_name", null: false
    t.text "filters"
    t.integer "status", default: 0, null: false
    t.text "error_message"
    t.integer "record_count"
    t.datetime "started_at"
    t.datetime "completed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_user_id"], name: "index_csv_exports_on_admin_user_id"
    t.index ["export_id"], name: "index_csv_exports_on_export_id", unique: true
  end

  create_table "impersonation_logs", force: :cascade do |t|
    t.bigint "admin_id", null: false
    t.bigint "user_id", null: false
    t.datetime "started_at", null: false
    t.datetime "ended_at"
    t.string "ip_address"
    t.string "user_agent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["admin_id"], name: "index_impersonation_logs_on_admin_id"
    t.index ["user_id"], name: "index_impersonation_logs_on_user_id"
  end

  create_table "job_applications", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.bigint "user_id", null: false
    t.integer "status", default: 0
    t.datetime "applied_at"
    t.datetime "accepted_at"
    t.datetime "rejected_at"
    t.text "application_letter"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "additional_info"
    t.boolean "salary_considered", default: false
    t.bigint "job_invitation_id"
    t.boolean "invited", default: false
    t.index ["created_at"], name: "index_job_applications_on_created_at"
    t.index ["job_id", "status"], name: "index_job_applications_on_job_id_and_status"
    t.index ["job_id", "user_id"], name: "index_job_applications_on_job_id_and_user_id", unique: true
    t.index ["job_id"], name: "index_job_applications_on_job_id"
    t.index ["job_invitation_id"], name: "index_job_applications_on_job_invitation_id"
    t.index ["status"], name: "index_job_applications_on_status"
    t.index ["user_id", "status"], name: "index_job_applications_on_user_id_and_status"
    t.index ["user_id"], name: "index_job_applications_on_user_id"
  end

  create_table "job_invitations", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.bigint "talent_profile_id", null: false
    t.text "invitation_letter"
    t.datetime "invited_at"
    t.integer "status", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id", null: false
    t.datetime "accepted_at"
    t.datetime "ignored_at"
    t.index ["job_id"], name: "index_job_invitations_on_job_id"
    t.index ["talent_profile_id"], name: "index_job_invitations_on_talent_profile_id"
    t.index ["user_id"], name: "index_job_invitations_on_user_id"
  end

  create_table "jobs", force: :cascade do |t|
    t.integer "job_category"
    t.string "title"
    t.text "description"
    t.integer "notification_preference"
    t.integer "outcome"
    t.integer "platform"
    t.integer "payment_frequency"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "organization_id", null: false
    t.string "status", default: "draft"
    t.datetime "application_deadline"
    t.string "client_count"
    t.string "charge_per_client"
    t.text "business_challenge"
    t.text "useful_info"
    t.text "offer_summary"
    t.integer "budget_range"
    t.string "topics", default: [], array: true
    t.datetime "expires_at"
    t.boolean "is_premium", default: false
    t.datetime "published_at"
    t.string "newsletter_frequency"
    t.string "newsletter_length"
    t.string "lead_magnet_type"
    t.string "work_duration"
    t.text "target_audience_description"
    t.text "emulated_brands_description"
    t.string "involvement_level"
    t.string "social_media_goal_type"
    t.boolean "social_media_understands_risk_acknowledged"
    t.text "requirements"
    t.text "headline"
    t.boolean "show_headline_in_form"
    t.integer "job_applications_count", default: 0, null: false
    t.index ["budget_range"], name: "index_jobs_on_budget_range"
    t.index ["expires_at"], name: "index_jobs_on_expires_at"
    t.index ["is_premium", "status"], name: "index_jobs_on_is_premium_and_status"
    t.index ["is_premium"], name: "index_jobs_on_is_premium"
    t.index ["job_category"], name: "index_jobs_on_job_category"
    t.index ["organization_id", "status"], name: "index_jobs_on_organization_id_and_status"
    t.index ["organization_id"], name: "index_jobs_on_organization_id"
    t.index ["platform"], name: "index_jobs_on_platform"
    t.index ["published_at"], name: "index_jobs_on_published_at"
    t.index ["status", "created_at"], name: "index_jobs_on_status_and_created_at"
    t.index ["status"], name: "index_jobs_on_status"
    t.index ["topics"], name: "index_jobs_on_topics", using: :gin
  end

  create_table "messages", force: :cascade do |t|
    t.bigint "conversation_id", null: false
    t.bigint "user_id", null: false
    t.text "body"
    t.datetime "read_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["conversation_id", "created_at"], name: "index_messages_on_conversation_id_and_created_at"
    t.index ["conversation_id"], name: "index_messages_on_conversation_id"
    t.index ["created_at"], name: "index_messages_on_created_at"
    t.index ["read_at"], name: "index_messages_on_read_at"
    t.index ["user_id"], name: "index_messages_on_user_id"
  end

  create_table "organization_memberships", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "organization_id", null: false
    t.integer "org_role", default: 0, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organization_id"], name: "index_organization_memberships_on_organization_id"
    t.index ["user_id", "organization_id"], name: "index_organization_memberships_on_user_id_and_organization_id", unique: true
    t.index ["user_id"], name: "index_organization_memberships_on_user_id"
  end

  create_table "organizations", force: :cascade do |t|
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "operating_timezone"
    t.string "size"
    t.integer "jobs_count", default: 0, null: false
    t.index ["created_at"], name: "index_organizations_on_created_at"
    t.index ["name"], name: "index_organizations_on_name"
  end

  create_table "pay_charges", force: :cascade do |t|
    t.bigint "customer_id", null: false
    t.bigint "subscription_id"
    t.string "processor_id", null: false
    t.integer "amount", null: false
    t.string "currency"
    t.integer "application_fee_amount"
    t.integer "amount_refunded"
    t.jsonb "metadata"
    t.jsonb "data"
    t.string "stripe_account"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "type"
    t.jsonb "object"
    t.index ["customer_id", "processor_id"], name: "index_pay_charges_on_customer_id_and_processor_id", unique: true
    t.index ["subscription_id"], name: "index_pay_charges_on_subscription_id"
  end

  create_table "pay_customers", force: :cascade do |t|
    t.string "owner_type"
    t.bigint "owner_id"
    t.string "processor", null: false
    t.string "processor_id"
    t.boolean "default"
    t.jsonb "data"
    t.string "stripe_account"
    t.datetime "deleted_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "type"
    t.jsonb "object"
    t.index ["owner_type", "owner_id", "deleted_at"], name: "pay_customer_owner_index", unique: true
    t.index ["processor", "processor_id"], name: "index_pay_customers_on_processor_and_processor_id", unique: true
  end

  create_table "pay_merchants", force: :cascade do |t|
    t.string "owner_type"
    t.bigint "owner_id"
    t.string "processor", null: false
    t.string "processor_id"
    t.boolean "default"
    t.jsonb "data"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "type"
    t.index ["owner_type", "owner_id", "processor"], name: "index_pay_merchants_on_owner_type_and_owner_id_and_processor"
  end

  create_table "pay_payment_methods", force: :cascade do |t|
    t.bigint "customer_id", null: false
    t.string "processor_id", null: false
    t.boolean "default"
    t.string "payment_method_type"
    t.jsonb "data"
    t.string "stripe_account"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "type"
    t.index ["customer_id", "processor_id"], name: "index_pay_payment_methods_on_customer_id_and_processor_id", unique: true
  end

  create_table "pay_subscriptions", force: :cascade do |t|
    t.bigint "customer_id", null: false
    t.string "name", null: false
    t.string "processor_id", null: false
    t.string "processor_plan", null: false
    t.integer "quantity", default: 1, null: false
    t.string "status", null: false
    t.datetime "current_period_start", precision: nil
    t.datetime "current_period_end", precision: nil
    t.datetime "trial_ends_at", precision: nil
    t.datetime "ends_at", precision: nil
    t.boolean "metered"
    t.string "pause_behavior"
    t.datetime "pause_starts_at", precision: nil
    t.datetime "pause_resumes_at", precision: nil
    t.decimal "application_fee_percent", precision: 8, scale: 2
    t.jsonb "metadata"
    t.jsonb "data"
    t.string "stripe_account"
    t.string "payment_method_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "type"
    t.jsonb "object"
    t.index ["customer_id", "processor_id"], name: "index_pay_subscriptions_on_customer_id_and_processor_id", unique: true
    t.index ["metered"], name: "index_pay_subscriptions_on_metered"
    t.index ["pause_starts_at"], name: "index_pay_subscriptions_on_pause_starts_at"
  end

  create_table "pay_webhooks", force: :cascade do |t|
    t.string "processor"
    t.string "event_type"
    t.jsonb "event"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "roles", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_at"], name: "index_roles_on_created_at"
    t.index ["name"], name: "index_roles_on_name", unique: true
  end

  create_table "saved_jobs", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "job_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["job_id"], name: "index_saved_jobs_on_job_id"
    t.index ["user_id"], name: "index_saved_jobs_on_user_id"
  end

  create_table "saved_searches", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "name", limit: 100, null: false
    t.string "resource_type", limit: 50, null: false
    t.text "search_params", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["resource_type"], name: "index_saved_searches_on_resource_type"
    t.index ["user_id", "name", "resource_type"], name: "index_saved_searches_on_user_id_and_name_and_resource_type", unique: true
    t.index ["user_id", "resource_type"], name: "index_saved_searches_on_user_id_and_resource_type"
    t.index ["user_id"], name: "index_saved_searches_on_user_id"
  end

  create_table "security_alerts", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "alert_type", null: false
    t.string "severity", null: false
    t.text "description", null: false
    t.json "metadata"
    t.datetime "resolved_at"
    t.bigint "resolved_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["alert_type"], name: "index_security_alerts_on_alert_type"
    t.index ["created_at"], name: "index_security_alerts_on_created_at"
    t.index ["resolved_at"], name: "index_security_alerts_on_resolved_at"
    t.index ["resolved_by_id"], name: "index_security_alerts_on_resolved_by_id"
    t.index ["severity"], name: "index_security_alerts_on_severity"
    t.index ["user_id", "created_at"], name: "index_security_alerts_on_user_id_and_created_at"
    t.index ["user_id"], name: "index_security_alerts_on_user_id"
  end

  create_table "session_activities", force: :cascade do |t|
    t.bigint "session_id", null: false
    t.string "activity_type", null: false
    t.string "controller"
    t.string "action"
    t.string "ip_address"
    t.string "user_agent"
    t.string "request_path"
    t.json "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["activity_type"], name: "index_session_activities_on_activity_type"
    t.index ["created_at"], name: "index_session_activities_on_created_at"
    t.index ["ip_address"], name: "index_session_activities_on_ip_address"
    t.index ["session_id", "created_at"], name: "index_session_activities_on_session_id_and_created_at"
    t.index ["session_id"], name: "index_session_activities_on_session_id"
  end

  create_table "sessions", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.string "user_agent"
    t.string "ip_address"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "last_activity_at"
    t.integer "security_warnings_count", default: 0, null: false
    t.integer "login_attempts", default: 0, null: false
    t.datetime "locked_until"
    t.index ["last_activity_at"], name: "index_sessions_on_last_activity_at"
    t.index ["locked_until"], name: "index_sessions_on_locked_until"
    t.index ["user_id"], name: "index_sessions_on_user_id"
  end

  create_table "talent_bookmarks", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "talent_profile_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["talent_profile_id"], name: "index_talent_bookmarks_on_talent_profile_id"
    t.index ["user_id", "talent_profile_id"], name: "index_talent_bookmarks_on_user_id_and_talent_profile_id", unique: true
    t.index ["user_id"], name: "index_talent_bookmarks_on_user_id"
  end

  create_table "talent_notes", force: :cascade do |t|
    t.bigint "talent_profile_id", null: false
    t.bigint "user_id", null: false
    t.bigint "organization_id", null: false
    t.bigint "last_modified_by_id"
    t.integer "category", default: 0
    t.boolean "pinned", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "content"
    t.index ["last_modified_by_id"], name: "index_talent_notes_on_last_modified_by_id"
    t.index ["organization_id"], name: "index_talent_notes_on_organization_id"
    t.index ["talent_profile_id"], name: "index_talent_notes_on_talent_profile_id"
    t.index ["user_id"], name: "index_talent_notes_on_user_id"
  end

  create_table "talent_profiles", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.text "bio"
    t.text "looking_for"
    t.string "skills", default: [], array: true
    t.text "about"
    t.string "vsl_link"
    t.integer "availability_status", default: 0
    t.decimal "price_range_min", precision: 10, scale: 2
    t.decimal "price_range_max", precision: 10, scale: 2
    t.integer "pricing_model", default: 0
    t.string "portfolio_link"
    t.string "linkedin_url"
    t.string "x_url"
    t.string "website_url"
    t.string "achievement_badges", default: [], array: true
    t.string "platform_choice"
    t.string "location"
    t.string "instagram_url"
    t.string "threads_url"
    t.string "niches", default: [], array: true
    t.string "outcomes", default: [], array: true
    t.string "ghostwriter_type", default: [], array: true
    t.string "social_media_specialty", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "headline"
    t.boolean "is_agency", default: false
    t.integer "location_preference", default: 0
    t.boolean "is_premium", default: false
    t.index ["is_premium"], name: "index_talent_profiles_on_is_premium"
    t.index ["location"], name: "index_talent_profiles_on_location"
    t.index ["skills"], name: "index_talent_profiles_on_skills", using: :gin
    t.index ["user_id", "is_premium"], name: "index_talent_profiles_on_user_id_and_is_premium"
    t.index ["user_id"], name: "index_talent_profiles_on_user_id"
  end

  create_table "user_roles", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "role_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_at"], name: "index_user_roles_on_created_at"
    t.index ["role_id"], name: "index_user_roles_on_role_id"
    t.index ["user_id", "role_id"], name: "index_user_roles_on_user_id_and_role_id", unique: true
    t.index ["user_id"], name: "index_user_roles_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", null: false
    t.string "password_digest", null: false
    t.boolean "verified", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "first_name"
    t.string "last_name"
    t.string "time_zone"
    t.boolean "onboarding_completed", default: false
    t.string "onboarding_step", default: "personal"
    t.datetime "verification_email_sent_at"
    t.boolean "scout_signup_completed", default: false
    t.boolean "talent_signup_completed", default: false
    t.integer "last_logged_in_organization_id"
    t.string "signup_intent"
    t.integer "conversations_count", default: 0, null: false
    t.integer "sent_chat_requests_count", default: 0, null: false
    t.integer "received_chat_requests_count", default: 0, null: false
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["first_name", "last_name"], name: "index_users_on_first_name_and_last_name"
    t.index ["first_name"], name: "index_users_on_first_name"
    t.index ["last_logged_in_organization_id"], name: "index_users_on_last_logged_in_organization_id"
    t.index ["last_name"], name: "index_users_on_last_name"
    t.index ["onboarding_completed"], name: "index_users_on_onboarding_completed"
    t.index ["scout_signup_completed"], name: "index_users_on_scout_signup_completed"
    t.index ["talent_signup_completed"], name: "index_users_on_talent_signup_completed"
    t.index ["verified", "created_at"], name: "index_users_on_verified_and_created_at"
    t.index ["verified"], name: "index_users_on_verified"
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "admin_audit_logs", "users", column: "admin_user_id"
  add_foreign_key "chat_requests", "users", column: "scout_id"
  add_foreign_key "chat_requests", "users", column: "talent_id"
  add_foreign_key "conversation_participants", "conversations"
  add_foreign_key "conversation_participants", "users"
  add_foreign_key "conversations", "jobs", on_delete: :nullify
  add_foreign_key "csv_exports", "users", column: "admin_user_id"
  add_foreign_key "impersonation_logs", "users"
  add_foreign_key "impersonation_logs", "users", column: "admin_id"
  add_foreign_key "job_applications", "job_invitations"
  add_foreign_key "job_applications", "jobs"
  add_foreign_key "job_applications", "users"
  add_foreign_key "job_invitations", "jobs"
  add_foreign_key "job_invitations", "talent_profiles"
  add_foreign_key "job_invitations", "users"
  add_foreign_key "jobs", "organizations"
  add_foreign_key "messages", "conversations"
  add_foreign_key "messages", "users"
  add_foreign_key "organization_memberships", "organizations"
  add_foreign_key "organization_memberships", "users"
  add_foreign_key "pay_charges", "pay_customers", column: "customer_id"
  add_foreign_key "pay_charges", "pay_subscriptions", column: "subscription_id"
  add_foreign_key "pay_payment_methods", "pay_customers", column: "customer_id"
  add_foreign_key "pay_subscriptions", "pay_customers", column: "customer_id"
  add_foreign_key "saved_jobs", "jobs"
  add_foreign_key "saved_jobs", "users"
  add_foreign_key "saved_searches", "users"
  add_foreign_key "security_alerts", "users"
  add_foreign_key "security_alerts", "users", column: "resolved_by_id"
  add_foreign_key "session_activities", "sessions"
  add_foreign_key "sessions", "users"
  add_foreign_key "talent_bookmarks", "talent_profiles"
  add_foreign_key "talent_bookmarks", "users"
  add_foreign_key "talent_notes", "organizations"
  add_foreign_key "talent_notes", "talent_profiles"
  add_foreign_key "talent_notes", "users"
  add_foreign_key "talent_notes", "users", column: "last_modified_by_id"
  add_foreign_key "talent_profiles", "users"
  add_foreign_key "user_roles", "roles"
  add_foreign_key "user_roles", "users"
end
